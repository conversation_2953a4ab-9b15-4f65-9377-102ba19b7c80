
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuthHook';
import { cn } from '@/lib/utils';
import { useState, useEffect } from 'react';
import { getDisplayVersion, getSystemStatus } from '@/lib/version';
import {
  LayoutGrid,
  Users,
  Warehouse,
  FileText,
  Receipt,
  PieChart,
  Calculator,
  Settings,
  BookOpen,
  CreditCard,
  Target,
  Calendar,
  CheckCircle,
  UserCog,
  Activity,
  Wifi,
  WifiOff,
  Info,
  ChevronDown,
  ChevronRight,
  Building2,
  Database,
  Shield,
  Bell,
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

export function Sidebar() {
  const location = useLocation();
  const { profile } = useAuth();
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Settings submenu items
  const settingsItems = [
    { name: 'Organization', href: '/settings/organization', icon: Building2 },
    { name: 'Data Backup', href: '/settings/backup', icon: Database },
    { name: 'Accounting', href: '/settings/accounting', icon: FileText },
    ...(profile?.role === 'admin' ? [
      { name: 'Security', href: '/settings/security', icon: Shield },
      { name: 'User Management', href: '/user-management', icon: UserCog },
    ] : []),
    { name: 'Notifications', href: '/settings/notifications', icon: Bell },
  ];

  // Check if any settings route is active
  const isSettingsActive = location.pathname.startsWith('/settings') || location.pathname === '/user-management';

  // Auto-expand settings if on a settings page
  useEffect(() => {
    if (isSettingsActive) {
      setIsSettingsOpen(true);
    }
  }, [isSettingsActive]);

  const navigation = [
    {
      name: 'Main',
      items: [{ name: 'Dashboard', href: '/dashboard', icon: LayoutGrid }],
    },
    {
      name: 'Accounting',
      items: [
        { name: 'Chart of Accounts', href: '/accounts', icon: BookOpen },
        { name: 'Journal Entries', href: '/journal-entries', icon: FileText },
        { name: 'Recurring Journals', href: '/recurring-journals', icon: Calendar },
      ],
    },
    {
      name: 'Sales & Purchases',
      items: [
        { name: 'Customers', href: '/customers', icon: Users },
        { name: 'Vendors', href: '/vendors', icon: Warehouse },
        { name: 'Invoices', href: '/invoices', icon: Receipt },
        { name: 'Bills', href: '/bills', icon: FileText },
        { name: 'Payments', href: '/payments', icon: CreditCard },
      ],
    },
    {
      name: 'Reports & Planning',
      items: [
        { name: 'Reports', href: '/reports', icon: PieChart },
        { name: 'Budgets', href: '/budgets', icon: Target },
        { name: 'Tax Management', href: '/tax-management', icon: Calculator },
      ],
    },
    {
      name: 'Administration',
      items: [
        { name: 'Audit Logs', href: '/audit-logs', icon: Activity },
      ],
    },
  ];

  return (
    <div className="h-full bg-sidebar-background text-sidebar-foreground flex flex-col p-4">
      <div className="flex h-full flex-col">
        <nav className="flex-1 space-y-4">
          {navigation.map((group) => (
            <div key={group.name} className="space-y-1">
              <h3 className="px-2 text-xs font-semibold text-sidebar-foreground/50 uppercase tracking-wider">
                {group.name}
              </h3>
              {group.items.map((item) => {
                const isActive = location.pathname === item.href;
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={cn(
                      'sidebar-item group flex items-center rounded-md py-2 px-2 text-sm font-medium transition-colors',
                      isActive
                        ? 'bg-sidebar-accent text-sidebar-accent-foreground'
                        : 'text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'
                    )}
                  >
                    <item.icon
                      className={cn(
                        'h-5 w-5 flex-shrink-0 mr-3',
                        isActive
                          ? 'text-sidebar-accent-foreground'
                          : 'text-sidebar-foreground'
                      )}
                    />
                    <span>{item.name}</span>
                  </Link>
                );
              })}
            </div>
          ))}

          {/* Settings Section */}
          <div className="space-y-1">
            <h3 className="px-2 text-xs font-semibold text-sidebar-foreground/50 uppercase tracking-wider">
              Settings
            </h3>
            <Collapsible open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
              <CollapsibleTrigger asChild>
                <button
                  className={cn(
                    'sidebar-item group flex items-center rounded-md py-2 px-2 text-sm font-medium transition-colors w-full',
                    isSettingsActive
                      ? 'bg-sidebar-accent text-sidebar-accent-foreground'
                      : 'text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'
                  )}
                >
                  <Settings
                    className={cn(
                      'h-5 w-5 flex-shrink-0 mr-3',
                      isSettingsActive
                        ? 'text-sidebar-accent-foreground'
                        : 'text-sidebar-foreground'
                    )}
                  />
                  <span className="flex-1 text-left">Settings</span>
                  {isSettingsOpen ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </button>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-1 mt-1">
                {settingsItems.map((item) => {
                  const isActive = location.pathname === item.href;
                  return (
                    <Link
                      key={item.name}
                      to={item.href}
                      className={cn(
                        'sidebar-item group flex items-center rounded-md py-2 px-2 ml-4 text-sm font-medium transition-colors',
                        isActive
                          ? 'bg-sidebar-accent text-sidebar-accent-foreground'
                          : 'text-sidebar-foreground/80 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'
                      )}
                    >
                      <item.icon
                        className={cn(
                          'h-4 w-4 flex-shrink-0 mr-3',
                          isActive
                            ? 'text-sidebar-accent-foreground'
                            : 'text-sidebar-foreground/80'
                        )}
                      />
                      <span>{item.name}</span>
                    </Link>
                  );
                })}
              </CollapsibleContent>
            </Collapsible>
          </div>
        </nav>

        {/* Sidebar Footer */}
        <div className="mt-auto pt-4 border-t border-sidebar-border">
          <TooltipProvider>
            <div className="space-y-2">
              {/* System Status */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-2 px-2 py-1 text-xs text-sidebar-foreground/70 hover:text-sidebar-foreground/90 transition-colors cursor-help">
                    {isOnline ? (
                      <>
                        <Wifi className="h-3 w-3 text-green-500" />
                        <span>Online</span>
                      </>
                    ) : (
                      <>
                        <WifiOff className="h-3 w-3 text-red-500" />
                        <span>Offline</span>
                      </>
                    )}
                    <Info className="h-3 w-3 ml-auto opacity-50" />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="right" className="max-w-xs">
                  <div className="space-y-1 text-xs">
                    <div><strong>Status:</strong> {getSystemStatus().status}</div>
                    <div><strong>Mode:</strong> {getSystemStatus().mode}</div>
                    <div><strong>Data:</strong> {getSystemStatus().dataSync}</div>
                    <div><strong>Last Sync:</strong> {getSystemStatus().lastSync}</div>
                  </div>
                </TooltipContent>
              </Tooltip>

              {/* Version */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center justify-between px-2 py-1 text-xs text-sidebar-foreground/50 hover:text-sidebar-foreground/70 transition-colors cursor-help">
                    <span>Version</span>
                    <span className="font-mono">v{getDisplayVersion()}</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent side="right" className="max-w-xs">
                  <div className="space-y-1 text-xs">
                    <div><strong>Version:</strong> {getDisplayVersion()}</div>
                    <div><strong>Environment:</strong> {import.meta.env.MODE}</div>
                    <div><strong>Build:</strong> {new Date().toLocaleDateString()}</div>
                    <div><strong>Platform:</strong> KAYA Finance</div>
                  </div>
                </TooltipContent>
              </Tooltip>
            </div>
          </TooltipProvider>
        </div>
      </div>
    </div>
  );
}
