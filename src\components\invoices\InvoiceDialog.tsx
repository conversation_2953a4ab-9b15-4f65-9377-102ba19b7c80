import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import { useToast } from '@/hooks/use-toast'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { useActiveCustomers, useActiveAccounts, useCreateInvoice } from '@/hooks/queries'
import type { Customer, Account } from '@/types/database'
import type { InvoiceStatus, InvoiceFormData, InvoiceLineData } from '@/types/invoices'
import { InvoiceLineItem } from './InvoiceLineItem'

interface InvoiceDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  customers?: Customer[]
  accounts?: Account[]
  onSuccess: () => void
  preselectedCustomerId?: string
}

export function InvoiceDialog({ open, onOpenChange, customers: propCustomers = [], accounts: propAccounts = [], onSuccess, preselectedCustomerId }: InvoiceDialogProps) {
  const { profile } = useAuth()
  const { toast } = useToast()
  const createInvoice = useCreateInvoice()
  const [loading, setLoading] = useState(false)

  // Use hooks to fetch data if not provided via props
  const { data: hookCustomers = [] } = useActiveCustomers()
  const { data: hookAccounts = [] } = useActiveAccounts()

  // Use prop data if available, otherwise use hook data
  const customers = propCustomers.length > 0 ? propCustomers : hookCustomers
  const accounts = propAccounts.length > 0 ? propAccounts : hookAccounts

  const [formData, setFormData] = useState<InvoiceFormData>({
    customer_id: preselectedCustomerId || '',
    account_id: '', // Add account_id to form data
    invoice_number: '',
    date_issued: new Date().toISOString().split('T')[0],
    due_date: '',
    notes: '',
    status: 'draft',
  })
  const [billLines, setBillLines] = useState<InvoiceLineData[]>([
    {
      account_id: '', // This will be updated when account is selected at form level
      description: '',
      quantity: 1,
      unit_price: 0,
      tax_rate_pct: 0,
    },
  ])

  // Update all line items when form-level account changes
  useEffect(() => {
    if (formData.account_id) {
      setBillLines(prevLines =>
        prevLines.map(line => ({
          ...line,
          account_id: formData.account_id
        }))
      )
    }
  }, [formData.account_id])

  // Update form data when preselected customer changes
  useEffect(() => {
    if (preselectedCustomerId) {
      setFormData(prev => ({ ...prev, customer_id: preselectedCustomerId }))
    }
  }, [preselectedCustomerId])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!profile?.org_id) return

    if (!formData.customer_id || billLines.length === 0) {
      toast({
        title: "Error",
        description: "Please select a customer and add at least one line item",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    try {
      const totals = billLines.reduce((acc, line) => {
        const lineTotal = line.quantity * line.unit_price
        const lineTax = lineTotal * (line.tax_rate_pct / 100)
        return {
          subtotal: acc.subtotal + lineTotal,
          taxAmount: acc.taxAmount + lineTax
        }
      }, { subtotal: 0, taxAmount: 0 })

      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const timestamp = Date.now().toString().slice(-4)
      const invoiceNumber = formData.invoice_number || `INV-${year}${month}-${timestamp}`

      const invoiceData = {
        customer_id: formData.customer_id,
        invoice_number: invoiceNumber,
        date_issued: formData.date_issued,
        due_date: formData.due_date,
        notes: formData.notes,
        status: formData.status,
        total_amount: totals.subtotal + totals.taxAmount,
        tax_amount: totals.taxAmount,
        lines: billLines.map(line => ({
          account_id: line.account_id && line.account_id !== '' ? line.account_id : null,
          description: line.description,
          quantity: line.quantity,
          unit_price: line.unit_price,
          tax_rate_pct: line.tax_rate_pct
        }))
      }

      await createInvoice.mutateAsync(invoiceData)

      onOpenChange(false)
      onSuccess()
    } catch (error) {
      console.error('Error creating invoice:', error)
      // Error handling is done by the React Query hook
    } finally {
      setLoading(false)
    }
  }

  const addLine = () => {
    setBillLines([
      ...billLines,
      {
        account_id: formData.account_id || '', // Use the form-level account selection
        description: '',
        quantity: 1,
        unit_price: 0,
        tax_rate_pct: 0,
      },
    ])
  }

  const removeLine = (index: number) => {
    setBillLines(billLines.filter((_, i) => i !== index))
  }

  const updateLine = (index: number, field: keyof InvoiceLineData, value: string | number) => {
    const newLines = [...billLines]
    newLines[index] = {
      ...newLines[index],
      [field]: value,
    }
    setBillLines(newLines)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Create New Invoice</DialogTitle>
          <DialogDescription>
            Create a new invoice for a customer
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="customer_id">Customer *</Label>
              <Select
                value={formData.customer_id}
                onValueChange={(value) => setFormData({ ...formData, customer_id: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select customer" />
                </SelectTrigger>
                <SelectContent>
                  {customers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="invoice_number">Invoice Number</Label>
              <Input
                id="invoice_number"
                value={formData.invoice_number}
                onChange={(e) => setFormData({ ...formData, invoice_number: e.target.value })}
                placeholder="Auto-generated if left empty"
              />
            </div>

            <div>
              <Label htmlFor="date_issued">Date Issued *</Label>
              <Input
                id="date_issued"
                type="date"
                value={formData.date_issued}
                onChange={(e) => setFormData({ ...formData, date_issued: e.target.value })}
                required
              />
            </div>

            <div>
              <Label htmlFor="due_date">Due Date *</Label>
              <Input
                id="due_date"
                type="date"
                value={formData.due_date}
                onChange={(e) => setFormData({ ...formData, due_date: e.target.value })}
                required
              />
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Line Items</h3>
              <div className="flex items-center gap-4">
                <div className="min-w-[200px]">
                  <Label htmlFor="account_id">Account *</Label>
                  <Select value={formData.account_id || ''} onValueChange={(value) => setFormData({ ...formData, account_id: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select account" />
                    </SelectTrigger>
                    <SelectContent>
                      {accounts.map((account) => (
                        <SelectItem key={account.id} value={account.id}>
                          {account.name} ({account.code})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <Button type="button" variant="outline" onClick={addLine} className="mt-6">
                  Add Line
                </Button>
              </div>
            </div>

            {billLines.map((line, index) => (
              <InvoiceLineItem
                key={index}
                line={line}
                index={index}
                onUpdate={updateLine}
                onRemove={removeLine}
                isLast={index === billLines.length - 1}
              />
            ))}
          </div>

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Input
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              placeholder="Add any additional notes"
            />
          </div>

          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Create Invoice'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
