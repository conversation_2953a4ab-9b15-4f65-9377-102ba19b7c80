// Temporary type extensions until generated types are updated
// This file extends the database types to include the newly added 'notes' field

import type { Customer as BaseCustomer, <PERSON><PERSON><PERSON> as BaseVendor } from './database'

// Extended Customer type with notes field
export interface CustomerWithNotes extends BaseCustomer {
  notes?: string | null
}

// Extended Vendor type with notes field  
export interface VendorWithNotes extends BaseVendor {
  notes?: string | null
}

// Re-export other types for convenience
export * from './database'
export type { CustomerWithNotes as Customer, Vendor<PERSON>ithNotes as Vendor }
