import type { Customer } from './extended-database'
import type { InvoiceStatus } from './database'

export interface InvoiceFormData {
  customer_id: string
  account_id: string
  invoice_number: string
  date_issued: string
  due_date: string
  notes: string
  status: InvoiceStatus
}

export interface InvoiceLineData {
  account_id?: string
  item: string
  description: string
  quantity: number
  unit_price: number
  tax_rate_pct: number
}

export interface InvoiceWithCustomer {
  id: string
  org_id: string
  customer_id: string
  invoice_number: string
  date_issued: string
  due_date: string
  total_amount: number
  tax_amount: number
  notes: string | null
  status: InvoiceStatus
  created_at: string
  updated_at: string
  customers: Customer | null
}

export interface InvoiceFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  editingInvoice: InvoiceWithCustomer | null
  customers: Customer[]
  accounts: Array<{ id: string; name: string; code: string }>
  onSubmit: (formData: InvoiceFormData, invoiceLines: InvoiceLineData[]) => Promise<void>
} 
