import { useState } from 'react'
import { format } from 'date-fns'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Search,
  Filter,
  Calendar,
  User,
  Eye,
  Download,
  RefreshCw,
  AlertTriangle
} from 'lucide-react'
import { LoadingSpinner } from '@/components/ui/loading'
import type { AuditLogWithProfile, AuditLogFilters } from '@/types/audit'
import { 
  AUDIT_ACTION_LABELS, 
  AUDIT_SEVERITY_COLORS, 
  ENTITY_TYPE_LABELS,
  getAuditLogIcon,
  formatAuditLogDescription,
  calculateRiskScore
} from '@/types/audit'

interface AuditLogListProps {
  logs: AuditLogWithProfile[]
  loading: boolean
  onFiltersChange: (filters: AuditLogFilters) => void
  onRefresh: () => void
  onExport: () => void
}

export function AuditLogList({ 
  logs, 
  loading, 
  onFiltersChange, 
  onRefresh, 
  onExport 
}: AuditLogListProps) {
  const [filters, setFilters] = useState<AuditLogFilters>({})
  const [selectedLog, setSelectedLog] = useState<AuditLogWithProfile | null>(null)
  const [showFilters, setShowFilters] = useState(false)

  const handleFilterChange = (key: keyof AuditLogFilters, value: string) => {
    const newFilters = { ...filters, [key]: value || undefined }
    setFilters(newFilters)
    onFiltersChange(newFilters)
  }

  const clearFilters = () => {
    setFilters({})
    onFiltersChange({})
  }

  const formatChangedData = (changedData: Record<string, unknown> | null | undefined) => {
    if (!changedData) return 'No details available'

    if (typeof changedData === 'object') {
      const changes = Object.entries(changedData)
        .map(([key, value]) => {
          if (typeof value === 'object' && value !== null && 'from' in value && 'to' in value) {
            const changeValue = value as { from: unknown; to: unknown }
            return `${key}: ${changeValue.from} → ${changeValue.to}`
          }
          return `${key}: ${value}`
        })
        .join(', ')
      return changes.length > 200 ? changes.substring(0, 200) + '...' : changes
    }

    return String(changedData)
  }

  const uniqueEntityTypes = [...new Set(logs.map(log => log.entity_type))]
  const uniqueActions = [...new Set(logs.map(log => log.action))]

  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Audit Logs</h2>
          <p className="text-muted-foreground">
            {logs.length} log{logs.length !== 1 ? 's' : ''} found
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          <Button variant="outline" size="sm" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm" onClick={onExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="search">Search</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="search"
                    placeholder="Search logs..."
                    value={filters.search || ''}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div>
                <Label>Entity Type</Label>
                <Select 
                  value={filters.entity_type || ''} 
                  onValueChange={(value) => handleFilterChange('entity_type', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Entities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Entities</SelectItem>
                    {uniqueEntityTypes.map(type => (
                      <SelectItem key={type} value={type}>
                        {ENTITY_TYPE_LABELS[type] || type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Action</Label>
                <Select 
                  value={filters.action || ''} 
                  onValueChange={(value) => handleFilterChange('action', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Actions" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Actions</SelectItem>
                    {uniqueActions.map(action => (
                      <SelectItem key={action} value={action}>
                        {AUDIT_ACTION_LABELS[action] || action}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="date_from">Date From</Label>
                <Input
                  id="date_from"
                  type="date"
                  value={filters.date_from || ''}
                  onChange={(e) => handleFilterChange('date_from', e.target.value)}
                />
              </div>
            </div>
            
            <div className="flex items-center gap-2 mt-4">
              <Button variant="outline" size="sm" onClick={clearFilters}>
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Audit Logs Table */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="p-8">
              <LoadingSpinner text="Loading audit logs..." showText />
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No audit logs found
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Action</TableHead>
                  <TableHead>Entity</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Risk</TableHead>
                  <TableHead>Details</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {logs.map((log) => {
                  const riskScore = calculateRiskScore(log.action, 'medium', log.entity_type)
                  return (
                    <TableRow key={log.id} className="hover:bg-muted/50">
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{getAuditLogIcon(log.action)}</span>
                          <div>
                            <Badge className="bg-blue-100 text-blue-800">
                              {AUDIT_ACTION_LABELS[log.action] || log.action}
                            </Badge>
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div>
                          <span className="font-medium">
                            {ENTITY_TYPE_LABELS[log.entity_type] || log.entity_type}
                          </span>
                          <p className="text-xs text-muted-foreground font-mono">
                            ID: {log.entity_id.slice(0, 8)}...
                          </p>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {log.profiles?.email || 'Unknown User'}
                          </span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm">
                              {format(new Date(log.created_at), 'MMM dd, yyyy')}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {format(new Date(log.created_at), 'HH:mm:ss')}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <Badge 
                          variant={riskScore > 7 ? 'destructive' : riskScore > 4 ? 'default' : 'secondary'}
                          className="flex items-center gap-1"
                        >
                          {riskScore > 6 && <AlertTriangle className="h-3 w-3" />}
                          {riskScore}/10
                        </Badge>
                      </TableCell>
                      
                      <TableCell>
                        <p className="text-sm text-muted-foreground max-w-[200px] truncate">
                          {formatChangedData(log.changed_data)}
                        </p>
                      </TableCell>
                      
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => setSelectedLog(log)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Audit Log Details</DialogTitle>
                            </DialogHeader>
                            {selectedLog && (
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <Label>Action</Label>
                                    <p className="text-sm font-medium">
                                      {AUDIT_ACTION_LABELS[selectedLog.action] || selectedLog.action}
                                    </p>
                                  </div>
                                  <div>
                                    <Label>Entity Type</Label>
                                    <p className="text-sm font-medium">
                                      {ENTITY_TYPE_LABELS[selectedLog.entity_type] || selectedLog.entity_type}
                                    </p>
                                  </div>
                                  <div>
                                    <Label>Entity ID</Label>
                                    <p className="text-sm font-mono">{selectedLog.entity_id}</p>
                                  </div>
                                  <div>
                                    <Label>User</Label>
                                    <p className="text-sm">{selectedLog.profiles?.email || 'Unknown'}</p>
                                  </div>
                                  <div>
                                    <Label>Date & Time</Label>
                                    <p className="text-sm">
                                      {format(new Date(selectedLog.created_at), 'PPpp')}
                                    </p>
                                  </div>
                                  <div>
                                    <Label>IP Address</Label>
                                    <p className="text-sm font-mono">{selectedLog.ip_address || 'N/A'}</p>
                                  </div>
                                </div>
                                
                                {selectedLog.changed_data && (
                                  <div>
                                    <Label>Changed Data</Label>
                                    <pre className="text-xs bg-muted p-3 rounded-md overflow-auto max-h-40">
                                      {JSON.stringify(selectedLog.changed_data, null, 2)}
                                    </pre>
                                  </div>
                                )}
                                
                                {selectedLog.user_agent && (
                                  <div>
                                    <Label>User Agent</Label>
                                    <p className="text-xs text-muted-foreground break-all">
                                      {selectedLog.user_agent}
                                    </p>
                                  </div>
                                )}
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
